<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直连上传测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin: 20px 0;
        }
        .test-case {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        .test-case:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
        }
        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        .upload-btn:disabled {
            background: #6c757d;
            transform: none;
            box-shadow: none;
        }
        .progress {
            height: 8px;
            border-radius: 10px;
            background: #f8f9fa;
            overflow: hidden;
        }
        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .mode-selector {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-check {
            background: white;
            border-radius: 10px;
            padding: 15px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .form-check:hover {
            border-color: #007bff;
        }
        .form-check-input:checked + .form-check-label {
            color: #007bff;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="test-container">
            <h1 class="text-center mb-4">
                <i class="bi bi-lightning-charge"></i>
                直连WebDAV上传测试
            </h1>
            <p class="text-center text-muted mb-4">
                测试直接连接WebDAV服务器进行文件上传，绕过CDN获得更快的上传速度
            </p>

            <!-- WebDAV选择器 -->
            <div class="mb-4">
                <label for="webdavSelect" class="form-label fw-bold">
                    <i class="bi bi-server"></i> 选择WebDAV服务
                </label>
                <select class="form-select" id="webdavSelect">
                    <option value="">加载中...</option>
                </select>
            </div>

            <!-- 上传模式选择 -->
            <div class="mode-selector">
                <label class="form-label fw-bold mb-3">
                    <i class="bi bi-upload"></i> 上传模式
                </label>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="uploadMode" id="proxyMode" value="proxy">
                            <label class="form-check-label" for="proxyMode">
                                <strong>代理上传</strong>
                                <small class="d-block text-muted">通过服务器代理上传</small>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="uploadMode" id="directMode" value="direct" checked>
                            <label class="form-check-label" for="directMode">
                                <strong>直连上传</strong>
                                <small class="d-block text-muted">直接连接WebDAV服务器</small>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试用例 -->
            <div class="test-case">
                <h5><i class="bi bi-file-earmark"></i> 测试1：小文件上传</h5>
                <p class="text-muted">上传一个小文件（&lt;1MB），测试基本功能</p>
                <input type="file" class="form-control mb-3" id="test1-file">
                <button class="upload-btn" onclick="testUpload('test1')">
                    <i class="bi bi-upload"></i> 开始测试
                </button>
                <div class="progress mt-3" id="test1-progress" style="display: none;">
                    <div class="progress-bar" id="test1-progress-bar"></div>
                </div>
                <div id="test1-result"></div>
            </div>

            <div class="test-case">
                <h5><i class="bi bi-file-earmark-image"></i> 测试2：图片文件上传</h5>
                <p class="text-muted">上传图片文件，测试预览功能</p>
                <input type="file" class="form-control mb-3" id="test2-file" accept="image/*">
                <button class="upload-btn" onclick="testUpload('test2')">
                    <i class="bi bi-upload"></i> 开始测试
                </button>
                <div class="progress mt-3" id="test2-progress" style="display: none;">
                    <div class="progress-bar" id="test2-progress-bar"></div>
                </div>
                <div id="test2-result"></div>
            </div>

            <div class="test-case">
                <h5><i class="bi bi-file-earmark-zip"></i> 测试3：大文件上传</h5>
                <p class="text-muted">上传较大文件（&gt;5MB），测试性能和稳定性</p>
                <input type="file" class="form-control mb-3" id="test3-file">
                <button class="upload-btn" onclick="testUpload('test3')">
                    <i class="bi bi-upload"></i> 开始测试
                </button>
                <div class="progress mt-3" id="test3-progress" style="display: none;">
                    <div class="progress-bar" id="test3-progress-bar"></div>
                </div>
                <div id="test3-result"></div>
            </div>

            <!-- 批量测试 -->
            <div class="test-case">
                <h5><i class="bi bi-files"></i> 批量测试</h5>
                <p class="text-muted">选择多个文件进行批量上传测试</p>
                <input type="file" multiple class="form-control mb-3" id="batch-files">
                <button class="upload-btn" onclick="batchTest()">
                    <i class="bi bi-upload"></i> 批量测试
                </button>
                <div id="batch-results"></div>
            </div>

            <!-- 性能对比 -->
            <div class="test-case">
                <h5><i class="bi bi-speedometer2"></i> 性能对比测试</h5>
                <p class="text-muted">同一文件分别使用代理上传和直连上传，对比速度</p>
                <input type="file" class="form-control mb-3" id="compare-file">
                <button class="upload-btn" onclick="compareUpload()">
                    <i class="bi bi-bar-chart"></i> 开始对比
                </button>
                <div id="compare-results"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let webdavConfigs = [];
        let appConfig = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            await loadConfigs();
        });

        async function loadConfigs() {
            try {
                // 加载应用配置
                const configResponse = await fetch('api.php?action=config');
                const configResult = await configResponse.json();
                if (configResult.success) {
                    appConfig = configResult.data;
                }

                // 加载WebDAV配置
                const webdavResponse = await fetch('api.php?action=webdav_list');
                const webdavResult = await webdavResponse.json();
                if (webdavResult.success) {
                    webdavConfigs = webdavResult.data;
                    populateWebDAVSelect();
                }
            } catch (error) {
                showResult('config-result', 'error', '加载配置失败: ' + error.message);
            }
        }

        function populateWebDAVSelect() {
            const select = document.getElementById('webdavSelect');
            select.innerHTML = '';
            
            if (webdavConfigs.length === 0) {
                select.innerHTML = '<option value="">没有可用的WebDAV配置</option>';
                return;
            }

            webdavConfigs.forEach(config => {
                const option = document.createElement('option');
                option.value = config.alias;
                option.textContent = config.name;
                select.appendChild(option);
            });

            if (webdavConfigs.length > 0) {
                select.value = webdavConfigs[0].alias;
            }
        }

        async function testUpload(testId) {
            const fileInput = document.getElementById(testId + '-file');
            const resultDiv = document.getElementById(testId + '-result');
            const progressDiv = document.getElementById(testId + '-progress');
            const progressBar = document.getElementById(testId + '-progress-bar');
            const uploadBtn = event.target;

            if (!fileInput.files.length) {
                showResult(resultDiv, 'error', '请先选择文件');
                return;
            }

            const file = fileInput.files[0];
            const webdavAlias = document.getElementById('webdavSelect').value;
            const uploadMode = document.querySelector('input[name="uploadMode"]:checked').value;

            if (!webdavAlias) {
                showResult(resultDiv, 'error', '请选择WebDAV服务');
                return;
            }

            uploadBtn.disabled = true;
            progressDiv.style.display = 'block';
            progressBar.style.width = '0%';

            const startTime = Date.now();
            showResult(resultDiv, 'info', `开始${uploadMode === 'direct' ? '直连' : '代理'}上传: ${file.name} (${formatFileSize(file.size)})`);

            try {
                if (uploadMode === 'direct') {
                    await directUpload(file, webdavAlias, progressBar, resultDiv, startTime);
                } else {
                    await proxyUpload(file, webdavAlias, progressBar, resultDiv, startTime);
                }
            } catch (error) {
                showResult(resultDiv, 'error', `上传失败: ${error.message}`);
            } finally {
                uploadBtn.disabled = false;
                progressDiv.style.display = 'none';
            }
        }

        async function directUpload(file, webdavAlias, progressBar, resultDiv, startTime) {
            // 获取WebDAV配置
            const configResponse = await fetch(`api.php?action=webdav_config&alias=${webdavAlias}`);
            const configResult = await configResponse.json();

            if (!configResult.success) {
                throw new Error(configResult.error || '获取WebDAV配置失败');
            }

            const webdavConfig = configResult.data;
            const safeFileName = generateSafeFileName(file.name);
            const uploadPath = webdavConfig.base_path.replace(/\/$/, '') + '/' + encodeURIComponent(safeFileName);
            const uploadUrl = webdavConfig.url.replace(/\/$/, '') + uploadPath;
            const auth = btoa(webdavConfig.username + ':' + webdavConfig.password);

            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();

                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressBar.style.width = percentComplete + '%';
                    }
                });

                xhr.addEventListener('load', () => {
                    const endTime = Date.now();
                    const duration = (endTime - startTime) / 1000;
                    const speed = (file.size / duration / 1024 / 1024).toFixed(2);

                    if (xhr.status >= 200 && xhr.status < 300) {
                        showResult(resultDiv, 'success',
                            `✅ 直连上传成功！<br>
                            文件名: ${safeFileName}<br>
                            耗时: ${duration.toFixed(2)}秒<br>
                            平均速度: ${speed} MB/s<br>
                            <a href="download.php?file=${encodeURIComponent(safeFileName)}&webdav=${webdavAlias}" target="_blank">下载链接</a>`
                        );
                        resolve();
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                });

                xhr.addEventListener('error', () => {
                    reject(new Error('网络错误'));
                });

                xhr.open('PUT', uploadUrl);
                xhr.setRequestHeader('Authorization', 'Basic ' + auth);
                xhr.setRequestHeader('Content-Type', file.type || 'application/octet-stream');
                xhr.send(file);
            });
        }

        async function proxyUpload(file, webdavAlias, progressBar, resultDiv, startTime) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('webdav', webdavAlias);

            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();

                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressBar.style.width = percentComplete + '%';
                    }
                });

                xhr.addEventListener('load', () => {
                    const endTime = Date.now();
                    const duration = (endTime - startTime) / 1000;
                    const speed = (file.size / duration / 1024 / 1024).toFixed(2);

                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.success) {
                                showResult(resultDiv, 'success',
                                    `✅ 代理上传成功！<br>
                                    文件名: ${response.data.file_name}<br>
                                    耗时: ${duration.toFixed(2)}秒<br>
                                    平均速度: ${speed} MB/s<br>
                                    <a href="${response.data.download_url}" target="_blank">下载链接</a>`
                                );
                                resolve();
                            } else {
                                reject(new Error(response.error));
                            }
                        } catch (e) {
                            reject(new Error('响应解析失败'));
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                });

                xhr.addEventListener('error', () => {
                    reject(new Error('网络错误'));
                });

                xhr.open('POST', 'api.php');
                xhr.send(formData);
            });
        }

        function generateSafeFileName(originalName) {
            const pathInfo = getPathInfo(originalName);
            const extension = pathInfo.extension ? '.' + pathInfo.extension.toLowerCase() : '';
            const timestamp = new Date().toISOString().replace(/[-:T]/g, '').split('.')[0];
            const random = Math.random().toString(36).substring(2, 10);
            const microtime = Date.now().toString().slice(-4);
            return `${timestamp}_${microtime}_${random}${extension}`;
        }

        function getPathInfo(filename) {
            const lastDotIndex = filename.lastIndexOf('.');
            if (lastDotIndex === -1) {
                return { name: filename, extension: '' };
            }
            return {
                name: filename.substring(0, lastDotIndex),
                extension: filename.substring(lastDotIndex + 1)
            };
        }

        async function batchTest() {
            const fileInput = document.getElementById('batch-files');
            const resultDiv = document.getElementById('batch-results');

            if (!fileInput.files.length) {
                showResult(resultDiv, 'error', '请先选择文件');
                return;
            }

            resultDiv.innerHTML = '<h6>批量上传测试结果:</h6>';
            const uploadMode = document.querySelector('input[name="uploadMode"]:checked').value;

            for (let i = 0; i < fileInput.files.length; i++) {
                const file = fileInput.files[i];
                const fileResultDiv = document.createElement('div');
                fileResultDiv.className = 'result info mt-2';
                fileResultDiv.innerHTML = `正在上传: ${file.name}...`;
                resultDiv.appendChild(fileResultDiv);

                try {
                    const startTime = Date.now();
                    const webdavAlias = document.getElementById('webdavSelect').value;

                    if (uploadMode === 'direct') {
                        await directUpload(file, webdavAlias, { style: { width: '0%' } }, fileResultDiv, startTime);
                    } else {
                        await proxyUpload(file, webdavAlias, { style: { width: '0%' } }, fileResultDiv, startTime);
                    }
                } catch (error) {
                    showResult(fileResultDiv, 'error', `❌ ${file.name}: ${error.message}`);
                }
            }
        }

        async function compareUpload() {
            const fileInput = document.getElementById('compare-file');
            const resultDiv = document.getElementById('compare-results');

            if (!fileInput.files.length) {
                showResult(resultDiv, 'error', '请先选择文件');
                return;
            }

            const file = fileInput.files[0];
            const webdavAlias = document.getElementById('webdavSelect').value;

            if (!webdavAlias) {
                showResult(resultDiv, 'error', '请选择WebDAV服务');
                return;
            }

            resultDiv.innerHTML = '<h6>性能对比测试:</h6>';

            // 测试代理上传
            const proxyResultDiv = document.createElement('div');
            proxyResultDiv.className = 'result info mt-2';
            proxyResultDiv.innerHTML = '正在测试代理上传...';
            resultDiv.appendChild(proxyResultDiv);

            try {
                const proxyStartTime = Date.now();
                await proxyUpload(file, webdavAlias, { style: { width: '0%' } }, proxyResultDiv, proxyStartTime);
            } catch (error) {
                showResult(proxyResultDiv, 'error', `代理上传失败: ${error.message}`);
            }

            // 等待1秒
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 测试直连上传
            const directResultDiv = document.createElement('div');
            directResultDiv.className = 'result info mt-2';
            directResultDiv.innerHTML = '正在测试直连上传...';
            resultDiv.appendChild(directResultDiv);

            try {
                const directStartTime = Date.now();
                await directUpload(file, webdavAlias, { style: { width: '0%' } }, directResultDiv, directStartTime);
            } catch (error) {
                showResult(directResultDiv, 'error', `直连上传失败: ${error.message}`);
            }
        }

        function showResult(element, type, message) {
            if (typeof element === 'string') {
                element = document.getElementById(element);
            }
            element.className = `result ${type}`;
            element.innerHTML = message;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
