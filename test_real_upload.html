<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .file-input {
            margin: 10px 0;
        }
        .upload-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        .upload-btn:hover {
            background: #0056b3;
        }
        .upload-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>文件上传测试页面</h1>
    <p>此页面用于测试修复后的文件上传功能，特别是长文件名和特殊字符的处理。</p>

    <div class="test-container">
        <h2>测试用例</h2>
        
        <div class="test-case">
            <h3>测试1：正常文件名</h3>
            <p>上传一个普通的文件名，如 "document.pdf" 或 "image.jpg"</p>
            <input type="file" class="file-input" id="test1-file">
            <button class="upload-btn" onclick="uploadFile('test1')">上传测试</button>
            <div class="progress" id="test1-progress" style="display: none;">
                <div class="progress-bar" id="test1-progress-bar"></div>
            </div>
            <div id="test1-result"></div>
        </div>

        <div class="test-case">
            <h3>测试2：长文件名</h3>
            <p>上传一个很长的文件名，测试系统如何处理</p>
            <input type="file" class="file-input" id="test2-file">
            <button class="upload-btn" onclick="uploadFile('test2')">上传测试</button>
            <div class="progress" id="test2-progress" style="display: none;">
                <div class="progress-bar" id="test2-progress-bar"></div>
            </div>
            <div id="test2-result"></div>
        </div>

        <div class="test-case">
            <h3>测试3：特殊字符文件名</h3>
            <p>上传包含空格、中文、特殊符号的文件名</p>
            <input type="file" class="file-input" id="test3-file">
            <button class="upload-btn" onclick="uploadFile('test3')">上传测试</button>
            <div class="progress" id="test3-progress" style="display: none;">
                <div class="progress-bar" id="test3-progress-bar"></div>
            </div>
            <div id="test3-result"></div>
        </div>

        <div class="test-case">
            <h3>测试4：中文文件名</h3>
            <p>上传纯中文文件名，测试Unicode支持</p>
            <input type="file" class="file-input" id="test4-file">
            <button class="upload-btn" onclick="uploadFile('test4')">上传测试</button>
            <div class="progress" id="test4-progress" style="display: none;">
                <div class="progress-bar" id="test4-progress-bar"></div>
            </div>
            <div id="test4-result"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>批量测试</h2>
        <p>选择多个不同类型的文件进行批量测试</p>
        <input type="file" multiple class="file-input" id="batch-files">
        <button class="upload-btn" onclick="batchUpload()">批量上传测试</button>
        <div id="batch-results"></div>
    </div>

    <script>
        // 获取WebDAV配置
        let webdavConfigs = [];
        
        async function loadWebDAVConfigs() {
            try {
                const response = await fetch('api.php?action=get_webdav_configs');
                const data = await response.json();
                if (data.success) {
                    webdavConfigs = data.data;
                }
            } catch (error) {
                console.error('加载WebDAV配置失败:', error);
            }
        }

        async function uploadFile(testId) {
            const fileInput = document.getElementById(testId + '-file');
            const resultDiv = document.getElementById(testId + '-result');
            const progressDiv = document.getElementById(testId + '-progress');
            const progressBar = document.getElementById(testId + '-progress-bar');
            const uploadBtn = document.querySelector(`[onclick="uploadFile('${testId}')"]`);

            if (!fileInput.files.length) {
                showResult(resultDiv, 'error', '请先选择文件');
                return;
            }

            const file = fileInput.files[0];
            uploadBtn.disabled = true;
            progressDiv.style.display = 'block';
            
            showResult(resultDiv, 'info', `开始上传文件: ${file.name} (${formatFileSize(file.size)})`);

            const formData = new FormData();
            formData.append('file', file);
            if (webdavConfigs.length > 0) {
                formData.append('webdav', webdavConfigs[0].alias);
            }

            try {
                const xhr = new XMLHttpRequest();

                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressBar.style.width = percentComplete + '%';
                    }
                });

                xhr.addEventListener('load', () => {
                    uploadBtn.disabled = false;
                    progressDiv.style.display = 'none';
                    
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.success) {
                                showResult(resultDiv, 'success', 
                                    `上传成功！<br>
                                    原文件名: ${response.data.original_name}<br>
                                    存储文件名: ${response.data.file_name}<br>
                                    文件大小: ${response.data.file_size_formatted}<br>
                                    下载链接: <a href="${response.data.download_url}" target="_blank">点击下载</a>`
                                );
                            } else {
                                showResult(resultDiv, 'error', `上传失败: ${response.error}`);
                            }
                        } catch (e) {
                            showResult(resultDiv, 'error', `响应解析失败: ${xhr.responseText}`);
                        }
                    } else {
                        showResult(resultDiv, 'error', `HTTP错误: ${xhr.status} - ${xhr.statusText}`);
                    }
                });

                xhr.addEventListener('error', () => {
                    uploadBtn.disabled = false;
                    progressDiv.style.display = 'none';
                    showResult(resultDiv, 'error', '网络错误，上传失败');
                });

                xhr.open('POST', 'api.php');
                xhr.send(formData);

            } catch (error) {
                uploadBtn.disabled = false;
                progressDiv.style.display = 'none';
                showResult(resultDiv, 'error', `上传异常: ${error.message}`);
            }
        }

        async function batchUpload() {
            const fileInput = document.getElementById('batch-files');
            const resultDiv = document.getElementById('batch-results');

            if (!fileInput.files.length) {
                showResult(resultDiv, 'error', '请先选择文件');
                return;
            }

            resultDiv.innerHTML = '<h3>批量上传结果:</h3>';

            for (let i = 0; i < fileInput.files.length; i++) {
                const file = fileInput.files[i];
                const fileResultDiv = document.createElement('div');
                fileResultDiv.className = 'test-case';
                fileResultDiv.innerHTML = `<h4>文件 ${i + 1}: ${file.name}</h4><div id="batch-result-${i}">上传中...</div>`;
                resultDiv.appendChild(fileResultDiv);

                await uploadSingleFile(file, `batch-result-${i}`);
            }
        }

        async function uploadSingleFile(file, resultId) {
            const resultDiv = document.getElementById(resultId);
            
            const formData = new FormData();
            formData.append('file', file);
            if (webdavConfigs.length > 0) {
                formData.append('webdav', webdavConfigs[0].alias);
            }

            try {
                const response = await fetch('api.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult(resultDiv, 'success', 
                        `✅ 上传成功<br>存储文件名: ${data.data.file_name}`
                    );
                } else {
                    showResult(resultDiv, 'error', `❌ 上传失败: ${data.error}`);
                }
            } catch (error) {
                showResult(resultDiv, 'error', `❌ 上传异常: ${error.message}`);
            }
        }

        function showResult(element, type, message) {
            element.className = `result ${type}`;
            element.innerHTML = message;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 页面加载时获取WebDAV配置
        document.addEventListener('DOMContentLoaded', loadWebDAVConfigs);
    </script>
</body>
</html>
